<template>
  <div class="config-step">
    <div class="panel-header">
      <div class="header-info">
        <h3 class="panel-title">
          <el-icon class="panel-icon"><Edit /></el-icon>
          配置数字人信息
        </h3>
        <p class="panel-desc">为每个数字人设置名称并匹配声音</p>
      </div>
    </div>

    <div class="config-content">


      <div class="config-grid">
        <div
          v-for="(human, index) in digitalHumans"
          :key="human.id"
          class="config-card"
          :style="{ animationDelay: `${index * 0.1}s` }"
        >
          <div class="card-header">
            <span class="card-number">{{ index + 1 }}</span>
            <h4 class="card-title">数字人 {{ index + 1 }}</h4>
          </div>
          <div class="card-body">
            <div class="avatar-section">
              <div class="avatar-preview">
                <video
                  v-if="avatarPreviews[human.avatarAddress]"
                  :src="avatarPreviews[human.avatarAddress]"
                  class="preview-video"
                  muted
                  loop
                  autoplay
                />
                <div v-else-if="human.avatarAddress" class="preview-loading">
                  <el-icon class="loading-icon"><Loading /></el-icon>
                  <span class="loading-text">加载中...</span>
                </div>
                <div v-else class="avatar-placeholder">
                  <el-icon class="avatar-icon"><Edit /></el-icon>
                </div>
              </div>
              <div v-if="human.avatarName" class="avatar-name">
                {{ human.avatarName }}
              </div>
            </div>
            <div class="form-section">
              <div class="form-item">
                <label class="form-label">数字人名称</label>
                <el-input 
                  v-model="human.name" 
                  placeholder="请输入数字人名称" 
                  maxlength="20" 
                  show-word-limit 
                  class="form-input"
                  @input="updateHumanName(human.id, human.name)"
                />
              </div>
              <div class="form-item">
                <label class="form-label">选择声音</label>
                <el-select 
                  v-model="human.voiceId" 
                  placeholder="请选择声音" 
                  @change="updateVoice(human)"
                  class="form-select"
                >
                  <el-option 
                    v-for="voice in selectedVoices" 
                    :key="voice.soundId" 
                    :label="voice.soundName" 
                    :value="voice.soundId" 
                  />
                </el-select>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Edit, Loading } from '@element-plus/icons-vue'
import { imageDetail } from '@/api/platform/image'

const props = defineProps({
  selectedAvatars: {
    type: Array,
    default: () => []
  },
  selectedVoices: {
    type: Array,
    default: () => []
  },
  digitalHumans: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update-digital-humans'])

// 组件内部状态
const avatarPreviews = ref({})

// 获取形象预览
const getAvatarPreview = async (imageAddress) => {
  if (!imageAddress || avatarPreviews.value[imageAddress]) {
    return avatarPreviews.value[imageAddress]
  }
  try {
    const response = await imageDetail(imageAddress)
    if (response && response.data) {
      const previewUrl = response.data.url || response.data
      avatarPreviews.value[imageAddress] = previewUrl
      return previewUrl
    }
  } catch (error) {
    console.error('获取形象预览失败:', error)
  }
  return null
}

// 加载所有形象预览
const loadAvatarPreviews = async () => {
  for (const human of props.digitalHumans) {
    if (human.avatarAddress) {
      await getAvatarPreview(human.avatarAddress)
    }
  }
}

// 监听digitalHumans变化
watch(() => props.digitalHumans, (newHumans) => {
  loadAvatarPreviews()
}, { immediate: true, deep: true })

const updateHumanName = (humanId, name) => {
  const updatedHumans = props.digitalHumans.map(human => {
    if (human.id === humanId) {
      return { ...human, name: name }
    }
    return human
  })
  emit('update-digital-humans', updatedHumans)
}

const updateVoice = (human) => {
  const voice = props.selectedVoices.find(v => v.soundId === human.voiceId)
  if (voice) {
    const updatedHumans = props.digitalHumans.map(h => {
      if (h.id === human.id) {
        return {
          ...h,
          voiceId: human.voiceId,
          voiceName: voice.soundName,
          voiceType: voice.voiceType || 'builtin'
        }
      }
      return h
    })
    emit('update-digital-humans', updatedHumans)
  }
}


</script>

<style lang="scss" scoped>
.config-step {
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 1px solid #e9ecef;

    .header-info {
      .panel-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin: 0 0 8px 0;

        .panel-icon {
          font-size: 28px;
          color: #667eea;
        }
      }

      .panel-desc {
        font-size: 16px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 24px;
    margin-top: 20px;
  }

  .config-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e9ecef;

      .card-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
      }

      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }
    }

    .card-body {
      padding: 24px;
      display: flex;
      gap: 20px;

      .avatar-section {
        flex-shrink: 0;
        text-align: center;

        .avatar-preview {
          width: 100px;
          height: 100px;
          border-radius: 12px;
          overflow: hidden;
          border: 2px solid #e9ecef;
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f8f9fa;

          .avatar-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .preview-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 8px;
          }

          .preview-loading {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            color: #909399;

            .loading-icon {
              font-size: 24px;
              margin-bottom: 8px;
              animation: rotate 1s linear infinite;
            }

            .loading-text {
              font-size: 12px;
            }
          }

          .avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;

            .avatar-icon {
              font-size: 32px;
              color: #ccc;
            }
          }

          .config-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .avatar-name {
          font-size: 12px;
          color: #666;
          text-align: center;
          margin-top: 8px;
          word-break: break-all;
          line-height: 1.2;
        }
      }

      .form-section {
        flex: 1;

        .form-item {
          margin-bottom: 20px;

          .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          .form-input,
          .form-select {
            width: 100%;
            border-radius: 8px;
          }
        }


      }
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 1200px) {
  .config-step .config-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .config-step .config-card .card-body {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}
</style>
